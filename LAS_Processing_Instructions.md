# LAS to GeoJSON Processing - Complete Instructions

## EC2 Instance Directory Structure

```
/home/<USER>/
├── las_env/                          # Python virtual environment
│   ├── bin/
│   │   ├── activate                  # Environment activation script
│   │   ├── python3                   # Python interpreter
│   │   └── pip                       # Package installer
│   ├── lib/
│   │   └── python3.12/
│   │       └── site-packages/        # All installed Python packages
│   │           ├── geopandas/
│   │           ├── laspy/
│   │           ├── osmnx/
│   │           ├── boto3/
│   │           ├── pyproj/
│   │           ├── shapely/
│   │           ├── pandas/
│   │           └── numpy/
│   └── pyvenv.cfg                    # Environment configuration
│
├── scripts/                          # Main application scripts
│   ├── map_api.py                    # Full lane processing script (MAIN)
│   ├── map_api_simple.py            # Simple road centerline script
│   └── batch_process.py              # Batch processing utility
│
├── .aws/                             # AWS configuration directory
│   ├── credentials                   # AWS access keys (auto-created)
│   └── config                        # AWS region settings (auto-created)
│
├── .bash_history                     # Command history
├── .bashrc                           # Shell configuration
└── .profile                          # User profile settings

Temporary Processing (created during execution):
/tmp/
└── las_processing/                   # Temporary processing directory
    ├── *.las                         # Downloaded LAS files (auto-deleted)
    ├── *.geojson                     # Generated GeoJSON files (auto-deleted)
    ├── lanes_tmp.geojson            # Temporary lane data
    └── markings_tmp.geojson         # Temporary marking data
```

## AWS S3 Bucket Structure

```
s3://las-processor/
├── input/                           # Upload your LAS files here
│   ├── HT447_1739357391_3217250_1423393178344338_1423393442211216.las
│   └── [your-other-las-files].las
│
├── results/                         # Generated GeoJSON outputs
│   ├── HT447_*_lanes.geojson       # Simple processing output
│   ├── HT447_*_lanes_full.geojson  # Full lane processing output
│   └── [other-outputs].geojson
│
└── maps/                           # Additional map data (optional)
```

## Step-by-Step Instructions

### 1. Connect to EC2 Instance

```bash
# From your local machine (Windows/Mac/Linux)
ssh -i C:\Users\<USER>\Desktop\API\las-processor.pem ubuntu@************

# Or from Windows Command Prompt:
ssh -i "C:\Users\<USER>\Desktop\API\las-processor.pem" ubuntu@************
```

### 2. Activate Python Environment

```bash
# Always run this first when you connect
source las_env/bin/activate

# You should see (las_env) in your prompt:
# (las_env) ubuntu@ip-172-31-43-29:~$
```

### 3. Navigate to Scripts Directory

```bash
cd ~/scripts
```

### 4. Upload LAS File to S3 (if not already uploaded)

```bash
# Upload from local file
aws s3 cp /path/to/your/file.las s3://las-processor/input/

# Or upload from EC2 instance if file is already there
aws s3 cp your-file.las s3://las-processor/input/
```

### 5. Check Available LAS Files

```bash
# List all files in S3 input directory
aws s3 ls s3://las-processor/input/

# Example output:
# 2025-05-23 06:52:01 2000000329 HT447_1739357391_3217250_1423393178344338_1423393442211216.las
```

### 6. Run Full Lane Processing

```bash
# Basic command structure:
python3 map_api.py --las_key input/YOUR_FILENAME.las --utm_epsg 32632

# Example with actual file:
python3 map_api.py --las_key input/HT447_1739357391_3217250_1423393178344338_1423393442211216.las --utm_epsg 32632
```

### 7. Monitor Processing

```bash
# The script will show progress:
# Downloading LAS file from S3: input/your-file.las
# Downloaded input/your-file.las from S3 to /tmp/las_processing/your-file.las
# Raw coordinates from LAS file: X: 123456 to 234567, Y: 5678901 to 6789012
# Converting coordinates from UTM (EPSG:32632) to WGS84
# Bounding box in WGS84: (9.123, 53.456) to (9.234, 53.567)
# Querying Overpass...
# Successfully wrote 1344 lane features and 0 marking features
# Uploaded /tmp/las_processing/your-file_lanes_full.geojson to S3
# Successfully uploaded GeoJSON to S3: s3://las-processor/results/your-file_lanes_full.geojson
```

### 8. Check Results

```bash
# List generated files
aws s3 ls s3://las-processor/results/

# Download result to check locally
aws s3 cp s3://las-processor/results/your-file_lanes_full.geojson /tmp/result.geojson

# Check file contents
head -20 /tmp/result.geojson
```

### 9. Verify Output Quality

```bash
# Check number of features
python3 -c "
import json
with open('/tmp/result.geojson', 'r') as f:
    data = json.load(f)
print(f'Total features: {len(data[\"features\"])}')
print(f'Sample properties: {data[\"features\"][0][\"properties\"]}')
"
```

## Common Commands Reference

### Environment Management
```bash
# Activate environment (always do this first)
source las_env/bin/activate

# Check installed packages
pip list

# Check Python version
python3 --version
```

### AWS S3 Operations
```bash
# List all buckets
aws s3 ls

# List bucket contents
aws s3 ls s3://las-processor/ --recursive

# Upload file
aws s3 cp local-file.las s3://las-processor/input/

# Download file
aws s3 cp s3://las-processor/results/output.geojson ./

# Check AWS configuration
aws configure list
```

### File System Navigation
```bash
# Go to home directory
cd ~

# Go to scripts directory
cd ~/scripts

# List files with details
ls -la

# Check disk space
df -h

# Check current directory
pwd
```

### Processing Scripts
```bash
# Full lane processing (recommended)
python3 map_api.py --las_key input/your-file.las --utm_epsg 32632

# Simple processing (for comparison)
python3 map_api_simple.py --las_key input/your-file.las --utm_epsg 32632

# Check script help
python3 map_api.py --help
```

## UTM EPSG Codes Reference

Choose the correct UTM zone for your area:

**Europe:**
- UTM Zone 32N: 32632 (Germany, Netherlands, Denmark)
- UTM Zone 33N: 32633 (Eastern Germany, Poland, Norway)
- UTM Zone 31N: 32631 (France, Spain, UK)

**North America:**
- UTM Zone 10N: 32610 (US West Coast)
- UTM Zone 11N: 32611 (California, Nevada)
- UTM Zone 17N: 32617 (US East Coast)
- UTM Zone 18N: 32618 (Eastern US, Canada)

**If unsure, use 32632 for Central Europe**

## Troubleshooting

### Connection Issues
```bash
# If SSH connection fails:
# 1. Check your key file path
# 2. Ensure key has correct permissions: chmod 400 las-processor.pem
# 3. Verify EC2 instance is running
```

### Environment Issues
```bash
# If packages not found:
source las_env/bin/activate
pip install --upgrade geopandas laspy osmnx boto3 pyproj shapely pandas numpy
```

### AWS Issues
```bash
# If S3 access denied:
aws configure
# Re-enter your AWS credentials
```

### Processing Issues
```bash
# If OSM query fails:
# Try with different UTM EPSG code
# Check internet connectivity: ping google.com
# Verify LAS file coordinates are reasonable
```

## Current Instance Status

**Active Files on EC2 Instance:**
```bash
# Check what's currently in the instance:
ubuntu@ip-172-31-43-29:~$ ls -la
total 112
drwxr-x--- 6 <USER> <GROUP>  4096 May 26 07:14 .
drwxr-xr-x 3 <USER>   <GROUP>    4096 May 22 15:02 ..
-rw------- 1 <USER> <GROUP> 71879 May 26 07:14 .bash_history
-rw-r--r-- 1 <USER> <GROUP>   220 Mar 31  2024 .bash_logout
-rw-r--r-- 1 <USER> <GROUP>  3771 Mar 31  2024 .bashrc
drwx------ 3 <USER> <GROUP>  4096 May 22 16:24 .cache
-rw------- 1 <USER> <GROUP>    57 May 22 17:10 .lesshst
-rw-r--r-- 1 <USER> <GROUP>   807 Mar 31  2024 .profile
drwx------ 2 <USER> <GROUP>  4096 May 22 17:10 .ssh
-rw-r--r-- 1 <USER> <GROUP>     0 May 22 15:37 .sudo_as_admin_successful
drwxrwxr-x 5 <USER> <GROUP>  4096 May 26 07:07 las_env
drwx------ 3 <USER> <GROUP>  4096 May 22 15:50 snap

# Virtual environment contents:
ubuntu@ip-172-31-43-29:~$ ls -la las_env/
total 24
drwxrwxr-x 5 <USER> <GROUP> 4096 May 26 07:07 .
drwxr-x--- 6 <USER> <GROUP> 4096 May 26 07:14 ..
drwxrwxr-x 2 <USER> <GROUP> 4096 May 26 07:08 bin
drwxrwxr-x 3 <USER> <GROUP> 4096 May 26 07:07 include
drwxrwxr-x 3 <USER> <GROUP> 4096 May 26 07:07 lib
lrwxrwxrwx 1 ubuntu ubuntu    3 May 26 07:07 lib64 -> lib
-rw-rw-r-- 1 <USER> <GROUP>  159 May 26 07:07 pyvenv.cfg

# Installed packages:
ubuntu@ip-172-31-43-29:~$ source las_env/bin/activate
(las_env) ubuntu@ip-172-31-43-29:~$ pip list
Package            Version
------------------ -----------
certifi            2025.4.26
charset-normalizer 3.4.2
geopandas          1.0.1
idna               3.10
laspy              2.5.4
networkx           3.4.2
numpy              2.2.6
osmnx              2.0.3
packaging          25.0
pandas             2.2.3
pip                24.0
pyogrio            0.11.0
pyproj             3.7.1
python-dateutil    2.9.0.post0
pytz               2025.2
requests           2.32.3
shapely            2.1.1
six                1.17.0
tzdata             2025.2
urllib3            2.4.0
```

**Scripts Directory:**
```bash
# Create and navigate to scripts directory:
(las_env) ubuntu@ip-172-31-43-29:~$ mkdir -p ~/scripts
(las_env) ubuntu@ip-172-31-43-29:~$ cd ~/scripts

# The main processing script is ready to use
# map_api.py contains the full lane processing implementation
```

**S3 Bucket Current Contents:**
```bash
# Input files available:
(las_env) ubuntu@ip-172-31-43-29:~/scripts$ aws s3 ls s3://las-processor/input/
2025-05-23 06:52:01 2000000329 HT447_1739357391_3217250_1423393178344338_1423393442211216.las

# Results generated:
(las_env) ubuntu@ip-172-31-43-29:~/scripts$ aws s3 ls s3://las-processor/results/
2025-05-26 15:XX:XX    52KB HT447_*_lanes.geojson          # Simple processing
2025-05-26 15:XX:XX   545KB HT447_*_lanes_full.geojson     # Full lane processing
```

## Expected Output

**Successful processing will generate:**
- **Input**: 1 LAS file (~2GB)
- **Output**: 1 GeoJSON file (~500KB)
- **Features**: 1000-2000 individual lane geometries
- **Properties**: lane_index, highway type, speed limits, turn information
- **Processing time**: 2-5 minutes depending on area size

**File naming convention:**
- Input: `your-filename.las`
- Output: `your-filename_lanes_full.geojson`

## Quick Start Commands

**Complete workflow in 5 commands:**
```bash
# 1. Connect to instance
ssh -i C:\Users\<USER>\Desktop\API\las-processor.pem ubuntu@************

# 2. Activate environment
source las_env/bin/activate

# 3. Go to scripts directory
cd ~/scripts

# 4. Check available files
aws s3 ls s3://las-processor/input/

# 5. Process your file (replace with your filename)
python3 map_api.py --las_key input/YOUR_FILENAME.las --utm_epsg 32632
```
