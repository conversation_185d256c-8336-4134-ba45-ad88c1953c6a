import os
import json
import boto3
import logging
import tempfile
from pathlib import Path
import precise_las_to_lane_geojson as processor

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize S3 client
s3 = boto3.client('s3')

def lambda_handler(event, context):
    """
    AWS Lambda handler to process LAS files from S3 and generate GeoJSON lane data
    
    Args:
        event: S3 event notification
        context: Lambda context
        
    Returns:
        Dict with processing results
    """
    logger.info(f"Received event: {json.dumps(event)}")
    
    # Get bucket and key from the event
    try:
        bucket = event['Records'][0]['s3']['bucket']['name']
        key = event['Records'][0]['s3']['object']['key']
        
        logger.info(f"Processing file {key} from bucket {bucket}")
        
        # Check if the file is a LAS file
        if not key.lower().endswith('.las'):
            logger.info(f"Skipping non-LAS file: {key}")
            return {
                'statusCode': 200,
                'body': json.dumps(f"Skipped non-LAS file: {key}")
            }
        
        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Download the LAS file
            las_path = os.path.join(temp_dir, os.path.basename(key))
            s3.download_file(bucket, key, las_path)
            
            logger.info(f"Downloaded {key} to {las_path}")
            
            # Determine output path
            output_filename = f"{Path(las_path).stem}_lanes.geojson"
            output_path = os.path.join(temp_dir, output_filename)
            
            # Process the LAS file
            # Default UTM EPSG code - can be parameterized or determined dynamically
            utm_epsg = 32632  # WGS 84 / UTM zone 32N
            
            logger.info(f"Processing LAS file with UTM EPSG: {utm_epsg}")
            
            try:
                # Process the LAS file
                result_path = processor.process_las_file(
                    las_path=las_path,
                    utm_epsg=utm_epsg,
                    output_path=output_path,
                    sample_rate=0.01,  # Can be parameterized
                    alpha=100.0        # Can be parameterized
                )
                
                logger.info(f"Processing complete. Result saved to {result_path}")
                
                # Upload the result to S3
                output_key = f"results/{output_filename}"
                s3.upload_file(result_path, bucket, output_key)
                
                logger.info(f"Uploaded result to s3://{bucket}/{output_key}")
                
                # Generate a pre-signed URL for the result
                url = s3.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': bucket, 'Key': output_key},
                    ExpiresIn=604800  # URL valid for 7 days
                )
                
                # Return success
                return {
                    'statusCode': 200,
                    'body': json.dumps({
                        'message': 'Processing complete',
                        'input_file': key,
                        'output_file': output_key,
                        'download_url': url
                    })
                }
                
            except Exception as e:
                logger.error(f"Error processing file: {str(e)}", exc_info=True)
                return {
                    'statusCode': 500,
                    'body': json.dumps(f"Error processing file: {str(e)}")
                }
    
    except Exception as e:
        logger.error(f"Error handling event: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'body': json.dumps(f"Error handling event: {str(e)}")
        }
