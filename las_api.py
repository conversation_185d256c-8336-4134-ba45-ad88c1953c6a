#!/usr/bin/env python3
"""
Flask API for processing LAS files and returning GeoJSON
"""

import os
import json
import tempfile
from pathlib import Path
import uuid
import boto3
from flask import Flask, request, jsonify, send_file
from werkzeug.utils import secure_filename

# Import the processing function
from process_las_with_lanes import process_las_with_lanes

app = Flask(__name__)

# Configure upload settings
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
RESULT_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'results')
ALLOWED_EXTENSIONS = {'las'}
MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100 MB

# Create folders if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULT_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULT_FOLDER'] = RESULT_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# S3 configuration
S3_BUCKET = 'las-processor'
s3_client = boto3.client('s3')

def allowed_file(filename):
    """Check if the file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """API home page"""
    return jsonify({
        "name": "LAS to GeoJSON API",
        "version": "1.0.0",
        "endpoints": {
            "/process": "POST - Process a LAS file and return GeoJSON",
            "/process_s3": "POST - Process a LAS file from S3 and return GeoJSON",
            "/health": "GET - Check API health"
        }
    })

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

@app.route('/process', methods=['POST'])
def process_las():
    """
    Process a LAS file uploaded via POST request
    
    Form parameters:
    - file: The LAS file to process
    - utm_epsg: (optional) UTM EPSG code (default: 32632)
    - sample_rate: (optional) Fraction of points to sample (default: 0.01)
    - alpha: (optional) Alpha value for the alpha shape algorithm (default: 100.0)
    
    Returns:
    - GeoJSON file
    """
    # Check if a file was uploaded
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400
    
    file = request.files['file']
    
    # Check if the file is empty
    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400
    
    # Check if the file is allowed
    if not allowed_file(file.filename):
        return jsonify({"error": f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"}), 400
    
    # Get processing parameters
    utm_epsg = request.form.get('utm_epsg', 32632, type=int)
    sample_rate = request.form.get('sample_rate', 0.01, type=float)
    alpha = request.form.get('alpha', 100.0, type=float)
    
    try:
        # Save the file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Process the file
        result = process_las_with_lanes(file_path, utm_epsg, sample_rate, alpha)
        
        if result['status'] == 'success' or result['status'] == 'partial_success':
            # Return the GeoJSON file
            return send_file(
                result['output_file'],
                mimetype='application/geo+json',
                as_attachment=True,
                download_name=os.path.basename(result['output_file'])
            )
        else:
            return jsonify({"error": result.get('error', 'Unknown error')}), 500
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/process_s3', methods=['POST'])
def process_las_s3():
    """
    Process a LAS file from S3
    
    JSON parameters:
    - bucket: S3 bucket name (default: 'las-processor')
    - key: S3 object key
    - utm_epsg: (optional) UTM EPSG code (default: 32632)
    - sample_rate: (optional) Fraction of points to sample (default: 0.01)
    - alpha: (optional) Alpha value for the alpha shape algorithm (default: 100.0)
    
    Returns:
    - GeoJSON file or JSON with S3 URL
    """
    # Get JSON data
    data = request.get_json()
    
    if not data:
        return jsonify({"error": "No JSON data provided"}), 400
    
    # Get parameters
    bucket = data.get('bucket', S3_BUCKET)
    key = data.get('key')
    utm_epsg = data.get('utm_epsg', 32632)
    sample_rate = data.get('sample_rate', 0.01)
    alpha = data.get('alpha', 100.0)
    
    if not key:
        return jsonify({"error": "No S3 object key provided"}), 400
    
    try:
        # Create a temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Download the LAS file from S3
            local_path = os.path.join(temp_dir, os.path.basename(key))
            s3_client.download_file(bucket, key, local_path)
            
            # Process the file
            result = process_las_with_lanes(local_path, utm_epsg, sample_rate, alpha)
            
            if result['status'] == 'success' or result['status'] == 'partial_success':
                # Upload the result to S3
                output_filename = os.path.basename(result['output_file'])
                output_key = f"results/{output_filename}"
                s3_client.upload_file(result['output_file'], bucket, output_key)
                
                # Generate a pre-signed URL for the result
                url = s3_client.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': bucket, 'Key': output_key},
                    ExpiresIn=3600  # URL valid for 1 hour
                )
                
                # Return the result with the S3 URL
                return jsonify({
                    "status": result['status'],
                    "input_file": key,
                    "output_file": output_key,
                    "download_url": url,
                    "bounds": result.get('bounds')
                })
            else:
                return jsonify({"error": result.get('error', 'Unknown error')}), 500
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
