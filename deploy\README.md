# LAS to GeoJSON Processor

This package processes LAS point cloud files and extracts lane information using OpenStreetMap data.

## Setup

1. Run the setup script:
   ```
   bash setup_ec2.sh
   ```

2. Process a LAS file from S3:
   ```
   source ~/las_processor_env/bin/activate
   python ~/las_processor/process_from_s3.py --bucket YOUR_BUCKET --key path/to/file.las
   ```

## Monitoring

The processing is monitored using CloudWatch. You can view logs in the AWS CloudWatch console.
