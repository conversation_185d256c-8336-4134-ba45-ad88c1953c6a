#!/usr/bin/env python3
"""
Map API Integration for LAS-to-GeoJSON Processor

This script provides functions to:
1. Upload GeoJSON data to map services
2. Generate map visualization URLs
3. Create embeddable map widgets
"""

import os
import json
import requests
import boto3
import logging
from urllib.parse import urlencode

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Map service endpoints
MAPBOX_UPLOAD_API = "https://api.mapbox.com/uploads/v1"
GEOJSON_IO_URL = "https://geojson.io/#data=data:application/json,"

class MapIntegration:
    """Class to handle map API integrations"""
    
    def __init__(self, mapbox_token=None):
        """
        Initialize the map integration
        
        Args:
            mapbox_token: Mapbox access token (optional)
        """
        self.mapbox_token = mapbox_token or os.environ.get('MAPBOX_TOKEN')
        self.s3 = boto3.client('s3')
    
    def generate_geojson_io_url(self, geojson_path):
        """
        Generate a URL to view the GeoJSON in geojson.io
        
        Args:
            geojson_path: Path to the GeoJSON file
            
        Returns:
            URL to view the GeoJSON in geojson.io
        """
        try:
            with open(geojson_path, 'r') as f:
                geojson_data = json.load(f)
            
            # Encode the GeoJSON data for URL
            encoded_data = urlencode({'data': json.dumps(geojson_data)})
            url = f"https://geojson.io/#{encoded_data}"
            
            logger.info(f"Generated geojson.io URL: {url}")
            return url
        
        except Exception as e:
            logger.error(f"Error generating geojson.io URL: {str(e)}")
            return None
    
    def generate_geojson_io_url_from_s3(self, bucket, key):
        """
        Generate a URL to view a GeoJSON file from S3 in geojson.io
        
        Args:
            bucket: S3 bucket name
            key: S3 object key
            
        Returns:
            URL to view the GeoJSON in geojson.io
        """
        try:
            # Download the GeoJSON file from S3
            response = self.s3.get_object(Bucket=bucket, Key=key)
            geojson_data = json.loads(response['Body'].read().decode('utf-8'))
            
            # Encode the GeoJSON data for URL
            encoded_data = urlencode({'data': json.dumps(geojson_data)})
            url = f"https://geojson.io/#{encoded_data}"
            
            logger.info(f"Generated geojson.io URL for s3://{bucket}/{key}: {url}")
            return url
        
        except Exception as e:
            logger.error(f"Error generating geojson.io URL from S3: {str(e)}")
            return None
    
    def upload_to_mapbox(self, geojson_path, tileset_name):
        """
        Upload GeoJSON to Mapbox as a tileset
        
        Args:
            geojson_path: Path to the GeoJSON file
            tileset_name: Name for the Mapbox tileset
            
        Returns:
            Dict with upload status and details
        """
        if not self.mapbox_token:
            logger.error("Mapbox token not provided")
            return {"status": "error", "message": "Mapbox token not provided"}
        
        try:
            # Create a temporary S3 location for Mapbox to access
            username = self._get_mapbox_username()
            if not username:
                return {"status": "error", "message": "Could not retrieve Mapbox username"}
            
            # Prepare the upload
            with open(geojson_path, 'rb') as f:
                geojson_data = f.read()
            
            # Create upload credentials
            credentials_url = f"{MAPBOX_UPLOAD_API}/{username}/credentials?access_token={self.mapbox_token}"
            credentials_response = requests.post(credentials_url)
            credentials = credentials_response.json()
            
            if 'accessKeyId' not in credentials:
                logger.error(f"Error getting Mapbox credentials: {credentials}")
                return {"status": "error", "message": "Failed to get Mapbox credentials"}
            
            # Upload to the temporary S3 location
            s3_client = boto3.client(
                's3',
                aws_access_key_id=credentials['accessKeyId'],
                aws_secret_access_key=credentials['secretAccessKey'],
                aws_session_token=credentials['sessionToken']
            )
            
            s3_client.put_object(
                Bucket=credentials['bucket'],
                Key=credentials['key'],
                Body=geojson_data
            )
            
            # Create the Mapbox upload
            upload_url = f"{MAPBOX_UPLOAD_API}/{username}?access_token={self.mapbox_token}"
            upload_params = {
                'url': credentials['url'],
                'tileset': f"{username}.{tileset_name}",
                'name': tileset_name
            }
            
            upload_response = requests.post(upload_url, json=upload_params)
            upload_data = upload_response.json()
            
            if 'id' not in upload_data:
                logger.error(f"Error creating Mapbox upload: {upload_data}")
                return {"status": "error", "message": "Failed to create Mapbox upload"}
            
            logger.info(f"Successfully created Mapbox upload: {upload_data['id']}")
            return {
                "status": "success",
                "upload_id": upload_data['id'],
                "tileset": upload_data['tileset'],
                "map_url": f"https://studio.mapbox.com/tilesets/{upload_data['tileset']}"
            }
        
        except Exception as e:
            logger.error(f"Error uploading to Mapbox: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _get_mapbox_username(self):
        """Get the Mapbox username from the API"""
        try:
            url = f"https://api.mapbox.com/user?access_token={self.mapbox_token}"
            response = requests.get(url)
            data = response.json()
            return data.get('username')
        except Exception as e:
            logger.error(f"Error getting Mapbox username: {str(e)}")
            return None
    
    def generate_map_html(self, geojson_url, title="LAS Processed Lanes"):
        """
        Generate HTML for an embeddable map
        
        Args:
            geojson_url: URL to the GeoJSON file
            title: Map title
            
        Returns:
            HTML string for the map
        """
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
    <style>
        body {{ margin: 0; padding: 0; }}
        #map {{ position: absolute; top: 0; bottom: 0; width: 100%; }}
        .map-overlay {{
            position: absolute;
            bottom: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.8);
            margin-right: 20px;
            font-family: Arial, sans-serif;
            overflow: auto;
            border-radius: 3px;
            padding: 10px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }}
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="map-overlay">
        <h3>{title}</h3>
        <p>Lane data extracted from LAS point cloud</p>
    </div>

    <script>
        // Initialize the map
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4M29iazA2Z2gycXA4N2pmbDZmangifQ.-g_vE53SD2WrJ6tFX7QHmA';
        const map = new mapboxgl.Map({{
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v12',
            zoom: 15
        }});

        map.on('load', () => {{
            // Add the GeoJSON source
            map.addSource('lanes', {{
                type: 'geojson',
                data: '{geojson_url}'
            }});

            // Add a layer for the lanes
            map.addLayer({{
                'id': 'lanes-layer',
                'type': 'line',
                'source': 'lanes',
                'layout': {{
                    'line-join': 'round',
                    'line-cap': 'round'
                }},
                'paint': {{
                    'line-color': [
                        'match',
                        ['get', 'highway'],
                        'motorway', '#ff0000',
                        'trunk', '#ff7f00',
                        'primary', '#ffff00',
                        'secondary', '#00ff00',
                        'tertiary', '#0000ff',
                        '#00ffff'  // default color
                    ],
                    'line-width': 3
                }}
            }});

            // Fit the map to the GeoJSON data
            map.fitBounds(turf.bbox(map.getSource('lanes')._data), {{
                padding: 50
            }});
        }});
    </script>
    <script src="https://unpkg.com/@turf/turf@6/turf.min.js"></script>
</body>
</html>"""
        return html

def main():
    """Command-line interface for map integration"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Map API Integration for LAS-to-GeoJSON Processor')
    parser.add_argument('--geojson', type=str, help='Path to GeoJSON file')
    parser.add_argument('--s3-bucket', type=str, help='S3 bucket containing GeoJSON')
    parser.add_argument('--s3-key', type=str, help='S3 key for GeoJSON file')
    parser.add_argument('--mapbox-token', type=str, help='Mapbox access token')
    parser.add_argument('--output-html', type=str, help='Path to save HTML map')
    parser.add_argument('--tileset-name', type=str, default='las_lanes', help='Name for Mapbox tileset')
    
    args = parser.parse_args()
    
    map_integration = MapIntegration(mapbox_token=args.mapbox_token)
    
    if args.geojson:
        # Generate geojson.io URL
        url = map_integration.generate_geojson_io_url(args.geojson)
        print(f"GeoJSON.io URL: {url}")
        
        # Upload to Mapbox if token provided
        if args.mapbox_token:
            result = map_integration.upload_to_mapbox(args.geojson, args.tileset_name)
            print(f"Mapbox upload result: {result}")
        
        # Generate HTML map if requested
        if args.output_html:
            # Create a pre-signed URL if the file is in S3
            if args.s3_bucket and args.s3_key:
                s3 = boto3.client('s3')
                geojson_url = s3.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': args.s3_bucket, 'Key': args.s3_key},
                    ExpiresIn=604800  # URL valid for 7 days
                )
            else:
                # Use a placeholder URL - in production you'd host this file somewhere
                geojson_url = f"file://{os.path.abspath(args.geojson)}"
            
            html = map_integration.generate_map_html(geojson_url)
            with open(args.output_html, 'w') as f:
                f.write(html)
            print(f"HTML map saved to {args.output_html}")
    
    elif args.s3_bucket and args.s3_key:
        # Generate geojson.io URL from S3
        url = map_integration.generate_geojson_io_url_from_s3(args.s3_bucket, args.s3_key)
        print(f"GeoJSON.io URL for S3 file: {url}")

if __name__ == "__main__":
    main()
