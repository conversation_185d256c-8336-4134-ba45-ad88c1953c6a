#!/usr/bin/env python3
"""
Extract detailed lane information from OpenStreetMap for the area covered by a LAS file
======================================================================================

This script:
1. Takes a LAS file path as input
2. Extracts coordinates from the LAS file
3. Queries OpenStreetMap for all road and lane data in that area
4. Retrieves ALL available OSM attributes
5. Generates lane geometries with proper offsets
6. Outputs a GeoJSON file with detailed lane information

Usage:
    python las_to_lane_geojson.py --las <path_to_las_file> [--utm_epsg <epsg_code>] [--output <output_file>]

Example:
    python las_to_lane_geojson.py --las data.las --utm_epsg 32632 --output lanes.geojson
"""

from __future__ import annotations

import argparse
import json
import os
import sys
from math import cos, radians
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import geopandas as gpd
import laspy
import osmnx as ox
import pandas as pd
from pyproj import CRS, Transformer
from shapely.geometry import LineString, Point
from shapely.ops import unary_union

# -----------  Parameters  ------------ #
LANE_WIDTH_M = 2.75                # Default lane width in meters (reduced for better alignment)
MAX_DOWNLOAD_METERS = 2000         # Maximum area to download from OSM
BUFFER_DISTANCE_M = 50             # Buffer around the LAS file area in meters
LANE_SPACING_M = 0.2               # Space between lanes (reduced for better visual alignment)
ALIGNMENT_CORRECTION = 0.0         # No additional correction factor
RIGHT_SIDE_SHIFT = -0.5            # Shift right-side lanes closer to centerline
LEFT_SIDE_SHIFT = 0.5              # Shift left-side lanes closer to centerline
# ------------------------------------ #


def extract_las_coordinates(las_path: str) -> Tuple[float, float, float, float]:
    """
    Extract the bounding box coordinates from a LAS file.

    Args:
        las_path: Path to the LAS file

    Returns:
        Tuple of (min_x, min_y, max_x, max_y) coordinates
    """
    print(f"Reading LAS file: {las_path}")
    las = laspy.read(las_path)
    points = las.xyz

    min_x, min_y = points[:, 0].min(), points[:, 1].min()
    max_x, max_y = points[:, 0].max(), points[:, 1].max()

    print(f"LAS file contains {len(points)} points")
    print(f"Raw coordinates: X: {min_x:.6f} to {max_x:.6f}, Y: {min_y:.6f} to {max_y:.6f}")

    return min_x, min_y, max_x, max_y


def convert_coordinates_to_wgs84(min_x: float, min_y: float, max_x: float, max_y: float,
                                utm_epsg: int) -> Tuple[float, float, float, float]:
    """
    Convert coordinates from UTM to WGS84 if needed.

    Args:
        min_x, min_y, max_x, max_y: Coordinates from the LAS file
        utm_epsg: EPSG code for the UTM coordinate system

    Returns:
        Tuple of (min_lon, min_lat, max_lon, max_lat) in WGS84
    """
    # Check if coordinates are already in WGS84 format
    if -180 <= min_x <= 180 and -180 <= max_x <= 180 and -90 <= min_y <= 90 and -90 <= max_y <= 90:
        print("Coordinates appear to be already in WGS84 format (longitude/latitude)")
        min_lon, min_lat = min_x, min_y
        max_lon, max_lat = max_x, max_y
    else:
        print(f"Converting coordinates from UTM (EPSG:{utm_epsg}) to WGS84")
        transformer = Transformer.from_crs(f"EPSG:{utm_epsg}", "EPSG:4326", always_xy=True)
        min_lon, min_lat = transformer.transform(min_x, min_y)
        max_lon, max_lat = transformer.transform(max_x, max_y)

    print(f"WGS84 bounding box: ({min_lon:.6f}, {min_lat:.6f}) to ({max_lon:.6f}, {max_lat:.6f})")
    return min_lon, min_lat, max_lon, max_lat


def get_osm_data(west: float, south: float, east: float, north: float) -> gpd.GeoDataFrame:
    """
    Query OpenStreetMap for all road and lane data in the specified area.
    Enhanced to retrieve more detailed lane information.

    Args:
        west, south, east, north: Bounding box coordinates in WGS84

    Returns:
        GeoDataFrame with OSM data
    """
    # Add a small buffer to ensure we get all relevant data
    center_lat = (north + south) / 2
    lat_buffer = BUFFER_DISTANCE_M / 111000  # approx conversion to degrees
    lon_buffer = BUFFER_DISTANCE_M / (111000 * cos(radians(center_lat)))

    bbox_buffered = (
        west - lon_buffer,
        south - lat_buffer,
        east + lon_buffer,
        north + lat_buffer
    )

    print(f"Querying OpenStreetMap with buffered bbox: {bbox_buffered}")

    # Define tags to query - we want ALL road-related features with enhanced lane information
    tags = {
        # Basic road information
        "highway": True,  # All roads

        # Lane count information
        "lanes": True,
        "lanes:forward": True,
        "lanes:backward": True,

        # Lane direction and turn information
        "turn:lanes": True,
        "turn:lanes:forward": True,
        "turn:lanes:backward": True,
        "change:lanes": True,
        "placement": True,

        # Lane destination information
        "destination:lanes": True,
        "destination:ref:lanes": True,
        "destination:symbol:lanes": True,
        "destination:colour:lanes": True,
        "destination:arrow:lanes": True,

        # Lane physical properties
        "maxspeed:lanes": True,
        "width:lanes": True,
        "width": True,  # Overall road width
        "surface": True,
        "smoothness": True,

        # Lane markings and road markings
        "lane_markings": True,
        "lane_markings:left": True,
        "lane_markings:right": True,
        "lane_markings:both": True,
        "road_marking": True,

        # Additional road properties that help with alignment
        "divider": True,
        "divider:width": True,
        "shoulder": True,
        "shoulder:width": True,
        "cycleway": True,
        "cycleway:width": True,
        "sidewalk": True,
        "sidewalk:width": True,
    }

    try:
        # Try with bbox approach first
        gdf = ox.features.features_from_bbox(
            bbox_buffered[3], bbox_buffered[1],
            bbox_buffered[2], bbox_buffered[0],
            tags=tags
        )
    except Exception as e:
        print(f"Error with bbox approach: {e}")
        print("Trying with point and radius approach...")

        # Fall back to point and radius approach
        center_lat = (north + south) / 2
        center_lon = (east + west) / 2

        # Calculate approximate radius in meters
        radius = max(
            abs(north - south) * 111000,  # height in meters
            abs(east - west) * 111000 * cos(radians(center_lat))  # width in meters
        ) / 2 + BUFFER_DISTANCE_M

        radius = min(radius, MAX_DOWNLOAD_METERS)  # Limit to avoid timeouts

        print(f"Using center point ({center_lon:.6f}, {center_lat:.6f}) with radius {radius:.1f}m")
        gdf = ox.features.features_from_point(
            (center_lat, center_lon),
            tags=tags,
            dist=radius
        )

    if gdf.empty:
        print("Warning: No OSM data found in this area")
        # Create an empty GeoDataFrame with a point geometry
        gdf = gpd.GeoDataFrame(
            {'id': [1], 'name': ['No roads found']},
            geometry=[Point((west + east) / 2, (south + north) / 2)],
            crs="EPSG:4326"
        )
    else:
        # Post-process to enhance lane information
        print(f"Retrieved {len(gdf)} features from OpenStreetMap")

        # Add missing columns that might be needed later
        required_columns = [
            "lanes", "width", "oneway", "highway", "osmid",
            "turn:lanes", "destination:lanes", "maxspeed", "surface"
        ]
        for col in required_columns:
            if col not in gdf.columns:
                gdf[col] = None

        # Ensure osmid is present (important for grouping)
        if "osmid" not in gdf.columns:
            # Create a unique identifier based on geometry
            gdf["osmid"] = gdf.index

        # Convert width values to numeric where possible
        if "width" in gdf.columns:
            gdf["width_numeric"] = pd.to_numeric(gdf["width"], errors="coerce")

    return gdf


def explode_lane_attributes(row: pd.Series) -> List[Dict]:
    """
    Turn composite *:lanes tags into one dict per lane with enhanced attribute handling.

    Args:
        row: A row from the GeoDataFrame

    Returns:
        List of dictionaries, one per lane
    """
    # Calculate total number of lanes with enhanced logic
    lanes_total = 1  # Default to 1 lane if no information is available

    # Try to get the total number of lanes from various sources
    lanes_value = row.get("lanes")
    lanes_forward = row.get("lanes:forward")
    lanes_backward = row.get("lanes:backward")

    # Calculate total lanes based on available information
    if pd.notna(lanes_value):
        try:
            lanes_total = int(lanes_value)
        except (ValueError, TypeError):
            # If conversion fails, try to extract numeric part
            import re
            match = re.search(r'\d+', str(lanes_value))
            if match:
                lanes_total = int(match.group())
    elif pd.notna(lanes_forward) and pd.notna(lanes_backward):
        # If we have forward and backward lanes, sum them
        try:
            lanes_total = int(lanes_forward) + int(lanes_backward)
        except (ValueError, TypeError):
            pass

    # Ensure we have at least one lane
    lanes_total = max(1, lanes_total)

    # Determine if this is a one-way road
    is_oneway = row.get("oneway") == "yes"

    # Get all lane-related attributes with enhanced coverage
    lane_attrs = {
        # Turn lanes
        "turn": row.get("turn:lanes"),
        "turn:forward": row.get("turn:lanes:forward"),
        "turn:backward": row.get("turn:lanes:backward"),

        # Destination information
        "destination": row.get("destination:lanes"),
        "destination:ref": row.get("destination:ref:lanes"),
        "destination:symbol": row.get("destination:symbol:lanes"),
        "destination:colour": row.get("destination:colour:lanes"),
        "destination:arrow": row.get("destination:arrow:lanes"),

        # Physical properties
        "maxspeed": row.get("maxspeed:lanes"),
        "width": row.get("width:lanes"),
        "surface": row.get("surface:lanes"),

        # Lane change information
        "change": row.get("change:lanes"),

        # Lane markings
        "marking": row.get("lane_markings:lanes"),
        "marking:left": row.get("lane_markings:left:lanes"),
        "marking:right": row.get("lane_markings:right:lanes"),
    }

    # Enhanced helper function to split lane values
    def split_lane_values(val):
        if not val or pd.isna(val):
            return [""] * lanes_total

        # Handle different separator formats
        if "|" in str(val):
            parts = str(val).split("|")
        elif ";" in str(val):
            parts = str(val).split(";")
        else:
            # If no separator, assume it applies to all lanes
            return [val] * lanes_total

        # Pad or truncate to match the number of lanes
        if len(parts) < lanes_total:
            parts += [""] * (lanes_total - len(parts))
        elif len(parts) > lanes_total:
            parts = parts[:lanes_total]

        return parts

    # Split all lane attributes
    lane_values = {k: split_lane_values(v) for k, v in lane_attrs.items()}

    # Create one record per lane with enhanced attributes
    records = []
    for idx in range(lanes_total):
        rec = row.to_dict()
        rec["lane_index"] = idx + 1
        rec["lane_total"] = lanes_total
        rec["is_oneway"] = is_oneway

        # Add lane position information
        if is_oneway:
            rec["lane_position"] = "right"  # All lanes are on the right for one-way
        else:
            # For two-way roads, determine if this lane is on the left or right side
            if idx < lanes_total / 2:
                rec["lane_position"] = "right"
            else:
                rec["lane_position"] = "left"

        # Add all lane-specific attributes
        for attr, values in lane_values.items():
            if values and values[0]:  # Only add if we have values
                rec[attr] = values[idx]

        # Add derived attributes that help with visualization
        highway_type = rec.get("highway", "")
        if isinstance(highway_type, str):
            if "motorway" in highway_type:
                rec["road_class"] = "motorway"
            elif "trunk" in highway_type:
                rec["road_class"] = "trunk"
            elif "primary" in highway_type:
                rec["road_class"] = "primary"
            elif "secondary" in highway_type:
                rec["road_class"] = "secondary"
            elif "tertiary" in highway_type:
                rec["road_class"] = "tertiary"
            elif "residential" in highway_type:
                rec["road_class"] = "residential"
            else:
                rec["road_class"] = "other"

        records.append(rec)

    return records


def create_lane_geometries(highways_gdf: gpd.GeoDataFrame, crs_m: CRS) -> gpd.GeoDataFrame:
    """
    Create lane geometries by offsetting the road centerlines with improved alignment.

    Args:
        highways_gdf: GeoDataFrame with highway features
        crs_m: Coordinate reference system in meters

    Returns:
        GeoDataFrame with lane geometries
    """
    # First explode the lanes
    all_lanes = []
    for _, row in highways_gdf.iterrows():
        all_lanes.extend(explode_lane_attributes(row))

    lanes_gdf = gpd.GeoDataFrame(all_lanes, crs=highways_gdf.crs)

    # Convert to a meter-based CRS for offsetting
    lanes_m = lanes_gdf.to_crs(crs_m)

    # Create offset geometries for each lane
    offset_lanes = []

    # Check if osmid column exists, if not, create a unique identifier
    if "osmid" not in lanes_m.columns:
        # Create a unique identifier based on geometry
        lanes_m["road_id"] = lanes_m.geometry.apply(lambda g: hash(g.wkt) % 10000)
        group_col = "road_id"
    else:
        group_col = "osmid"

    # Group by road to ensure consistent offsets within the same road
    for _, road_group in lanes_m.groupby(group_col, dropna=False):
        # Get the total number of lanes for this road
        road_lanes_value = road_group.get("lanes", 1).iloc[0] if not road_group.empty else 1
        road_lanes_total = int(road_lanes_value) if pd.notna(road_lanes_value) else 1

        # Determine if this is a one-way road
        is_oneway = road_group.get("oneway", "no").iloc[0] == "yes" if not road_group.empty else False

        # Adjust lane width based on road type with improved values for better alignment
        lane_width = LANE_WIDTH_M
        if not road_group.empty:
            highway_type = road_group["highway"].iloc[0]

            # Adjust lane width based on highway type - using narrower values for better alignment
            if highway_type in ["motorway", "motorway_link"]:
                lane_width = 2.9  # Slightly narrower lanes for motorways
            elif highway_type in ["trunk", "trunk_link"]:
                lane_width = 2.8  # Slightly narrower lanes for trunk roads
            elif highway_type in ["primary", "primary_link"]:
                lane_width = 2.7  # Narrower lanes for primary roads
            elif highway_type in ["secondary", "secondary_link"]:
                lane_width = 2.6  # Narrower lanes for secondary roads
            elif highway_type in ["residential", "tertiary", "tertiary_link"]:
                lane_width = 2.5  # Narrower lanes for smaller roads
            elif highway_type in ["track", "service"]:
                lane_width = 2.0  # Very narrow lanes for tracks and service roads

            # Further adjust based on any width information
            if "width" in road_group.columns and pd.notna(road_group["width"].iloc[0]):
                try:
                    # If width is specified for the whole road, divide by number of lanes
                    road_width = float(road_group["width"].iloc[0])
                    if road_width > 0 and road_lanes_total > 0:
                        # Use the specified width but cap it to reasonable values
                        calculated_width = road_width / road_lanes_total
                        lane_width = min(max(calculated_width, 2.0), 3.5)  # Between 2.0 and 3.5 meters
                except (ValueError, TypeError):
                    # Keep default if conversion fails
                    pass

            # Further adjust based on number of lanes (more lanes = narrower lanes)
            if road_lanes_total > 2:
                lane_width *= 0.95  # 5% narrower for roads with more than 2 lanes

        # Process each lane in this road
        for _, row in road_group.iterrows():
            lane_idx = row["lane_index"]

            # Calculate offset based on lane index and total lanes with improved alignment
            if is_oneway:
                # For one-way roads, offset all lanes to the right of centerline
                # Adjust lane width based on total lanes (narrower for more lanes)
                adjusted_width = lane_width * (0.9 if road_lanes_total > 2 else 1.0)

                # Calculate base offset with spacing
                base_offset = adjusted_width * (lane_idx - 0.5) + LANE_SPACING_M * (lane_idx - 1)

                # Apply right-side shift to bring lanes closer to centerline
                offset = -(base_offset + RIGHT_SIDE_SHIFT)
                side = "right"
            else:
                # For two-way roads, offset based on lane index
                if lane_idx <= road_lanes_total / 2:
                    # Lanes on the right side
                    # Adjust lane width based on total lanes (narrower for more lanes)
                    adjusted_width = lane_width * (0.9 if road_lanes_total > 4 else 1.0)

                    # Calculate base offset with spacing
                    base_offset = adjusted_width * (lane_idx - 0.5) + LANE_SPACING_M * (lane_idx - 1)

                    # Apply right-side shift to bring lanes closer to centerline
                    offset = -(base_offset + RIGHT_SIDE_SHIFT)
                    side = "right"
                else:
                    # Lanes on the left side
                    left_idx = lane_idx - road_lanes_total / 2

                    # Adjust lane width based on total lanes (narrower for more lanes)
                    adjusted_width = lane_width * (0.9 if road_lanes_total > 4 else 1.0)

                    # Calculate base offset with spacing
                    base_offset = adjusted_width * (left_idx - 0.5) + LANE_SPACING_M * (left_idx - 1)

                    # Apply left-side shift to bring lanes closer to centerline
                    offset = base_offset + LEFT_SIDE_SHIFT
                    side = "left"

            # Create the offset geometry with improved join style for better alignment
            try:
                # Use join_style=1 (round) for smoother curves
                offset_geom = row.geometry.parallel_offset(abs(offset), side=side, join_style=1)

                # Check if the offset geometry is valid
                if offset_geom.is_empty or not offset_geom.is_valid:
                    # Try with a different join style
                    offset_geom = row.geometry.parallel_offset(abs(offset), side=side, join_style=2)

                    # If still invalid, fall back to original
                    if offset_geom.is_empty or not offset_geom.is_valid:
                        offset_geom = row.geometry
            except (ValueError, AttributeError, TypeError) as e:
                # Fall back to original geometry if offset fails
                print(f"Warning: Failed to offset geometry: {e}")
                offset_geom = row.geometry

            # Create a new record with the offset geometry
            new_row = row.copy()
            new_row["geometry"] = offset_geom
            new_row["offset_meters"] = offset
            new_row["lane_width"] = lane_width
            offset_lanes.append(new_row)

    # Create a new GeoDataFrame with the offset lanes
    offset_gdf = gpd.GeoDataFrame(offset_lanes, crs=crs_m)

    # Convert back to WGS84
    return offset_gdf.to_crs("EPSG:4326")


def create_geojson_output(lanes_gdf: gpd.GeoDataFrame, markings_gdf: gpd.GeoDataFrame,
                         output_path: str) -> str:
    """
    Create a GeoJSON file with lanes and road markings with enhanced styling information.

    Args:
        lanes_gdf: GeoDataFrame with lane geometries
        markings_gdf: GeoDataFrame with road marking geometries
        output_path: Path to save the GeoJSON file

    Returns:
        Path to the saved GeoJSON file
    """
    # Create temporary files
    tmp_dir = Path(output_path).parent
    lanes_tmp = tmp_dir / "lanes_tmp.geojson"
    marks_tmp = tmp_dir / "markings_tmp.geojson"

    # Save to temporary files
    lanes_gdf.to_file(lanes_tmp, driver="GeoJSON")
    if not markings_gdf.empty:
        markings_gdf.to_file(marks_tmp, driver="GeoJSON")

    # Load the temporary files
    with open(lanes_tmp) as f:
        lanes_fc = json.load(f)

    if marks_tmp.exists():
        with open(marks_tmp) as f:
            marks_fc = json.load(f)
    else:
        marks_fc = {"type": "FeatureCollection", "features": []}

    # Enhanced lane features with styling information
    enhanced_lane_features = []
    for f in lanes_fc["features"]:
        props = f["properties"]

        # Add layer information
        props["layer"] = "lane"

        # Add styling information based on road class - using bright green for all lanes to match the image
        props["stroke"] = "#00FF00"  # Bright green for all lanes to match the image

        # Adjust stroke width based on road class
        road_class = props.get("road_class", "other")
        if road_class == "motorway":
            props["stroke-width"] = 1.5  # Thinner lines for better alignment
        elif road_class == "trunk":
            props["stroke-width"] = 1.4
        elif road_class == "primary":
            props["stroke-width"] = 1.3
        elif road_class == "secondary":
            props["stroke-width"] = 1.2
        else:
            props["stroke-width"] = 1.0

        # Set high opacity for all lanes
        props["stroke-opacity"] = 1.0

        # Add lane-specific styling
        lane_position = props.get("lane_position", "right")
        lane_index = props.get("lane_index", 1)
        lane_total = props.get("lane_total", 1)

        # Adjust styling for specific lane positions
        if lane_position == "left":
            # Left-side lanes
            if lane_index == lane_total:  # Leftmost lane
                props["stroke-width"] = props["stroke-width"] * 1.1  # Slightly thicker
        else:
            # Right-side lanes
            if lane_index == 1:  # Rightmost lane
                props["stroke-width"] = props["stroke-width"] * 1.1  # Slightly thicker

        # Add turn lane styling
        turn = props.get("turn", "")
        if turn and isinstance(turn, str):
            if "left" in turn:
                props["stroke-dasharray"] = "5, 2"
            elif "right" in turn:
                props["stroke-dasharray"] = "2, 2"
            elif "through" in turn:
                # No dash for through lanes
                pass
            else:
                # Default dash for other turn types
                props["stroke-dasharray"] = "1, 1"

        # Create the enhanced feature
        enhanced_lane_features.append({
            "type": "Feature",
            "properties": props,
            "geometry": f["geometry"]
        })

    # Enhanced marking features
    enhanced_marking_features = []
    for f in marks_fc["features"]:
        props = f["properties"]

        # Add layer information
        props["layer"] = "road_marking"

        # Add styling for road markings
        props["stroke"] = "#FFFF00"  # Yellow for road markings
        props["stroke-width"] = 1
        props["stroke-opacity"] = 0.8

        # Create the enhanced feature
        enhanced_marking_features.append({
            "type": "Feature",
            "properties": props,
            "geometry": f["geometry"]
        })

    # Combine into a single GeoJSON with enhanced metadata
    out_fc = {
        "type": "FeatureCollection",
        "name": "lanes_and_markings",
        "crs": {
            "type": "name",
            "properties": {
                "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
            }
        },
        "features": enhanced_lane_features + enhanced_marking_features,
    }

    # Save the combined GeoJSON
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(out_fc, f, ensure_ascii=False)

    # Clean up temporary files
    lanes_tmp.unlink(missing_ok=True)
    marks_tmp.unlink(missing_ok=True)

    print(f"Successfully wrote {len(lanes_gdf)} lane features and {len(markings_gdf)} marking features to {output_path}")
    return output_path


def process_las_file(las_path: str, utm_epsg: int, output_path: Optional[str] = None) -> str:
    """
    Process a LAS file to extract lane information from OpenStreetMap.

    Args:
        las_path: Path to the LAS file
        utm_epsg: EPSG code for the UTM coordinate system
        output_path: Path to save the GeoJSON file (optional)

    Returns:
        Path to the saved GeoJSON file
    """
    # Extract coordinates from the LAS file
    min_x, min_y, max_x, max_y = extract_las_coordinates(las_path)

    # Convert coordinates to WGS84 if needed
    min_lon, min_lat, max_lon, max_lat = convert_coordinates_to_wgs84(
        min_x, min_y, max_x, max_y, utm_epsg
    )

    # Get OSM data for the area
    osm_data = get_osm_data(min_lon, min_lat, max_lon, max_lat)

    # Split highways and road markings
    highways = osm_data[osm_data["highway"].notna()].copy() if "highway" in osm_data.columns else gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")
    markings = osm_data[osm_data["road_marking"].notna()].copy() if "road_marking" in osm_data.columns else gpd.GeoDataFrame(geometry=[], crs="EPSG:4326")

    # Filter out non-LineString geometries for highways (to avoid errors with parallel_offset)
    highways = highways[highways.geometry.type.isin(['LineString', 'MultiLineString'])].copy()

    # Create lane geometries
    crs_m = CRS.from_epsg(utm_epsg)
    lanes_gdf = create_lane_geometries(highways, crs_m)

    # Determine output path if not provided
    if not output_path:
        output_dir = Path(las_path).with_suffix("")
        output_dir.mkdir(exist_ok=True)
        output_path = str(output_dir / f"{Path(las_path).stem}_lanes.geojson")

    # Create GeoJSON output
    return create_geojson_output(lanes_gdf, markings, output_path)


def main():
    """Main function to parse arguments and process the LAS file."""
    parser = argparse.ArgumentParser(description='Extract detailed lane information from a LAS file using OpenStreetMap')
    parser.add_argument('--las', type=str, required=True, help='Path to the LAS file')
    parser.add_argument('--utm_epsg', type=int, default=32632, help='UTM EPSG code for coordinate transformation')
    parser.add_argument('--output', type=str, help='Path to save the GeoJSON file (optional)')

    args = parser.parse_args()

    # Check if the LAS file exists
    if not os.path.exists(args.las):
        sys.exit(f"Error: LAS file not found at {args.las}")

    try:
        # Process the LAS file
        output_path = process_las_file(args.las, args.utm_epsg, args.output)
        print(f"GeoJSON with detailed lane information saved to: {output_path}")
        print(f"You can view this file at https://geojson.io")
    except Exception as e:
        print(f"Error processing file: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
