# import laspy
# from pyproj import Transformer
# import osmnx as ox
# import geopandas as gpd
# import os
# import sys
# from math import cos, radians

# def las_to_osm_geojson(las_file_path, utm_epsg):
#     """
#     Takes a LAS file path and its UTM EPSG code, extracts the bounding box,
#     queries OSM for road data within that bounding box, and saves the result as GeoJSON.

#     Parameters:
#     - las_file_path (str): Path to the LAS file.
#     - utm_epsg (int): EPSG code for the UTM coordinate system of the LAS file.

#     Returns:
#     - str: Path to the output GeoJSON file.
#     """

#     # Step 1: Read the LAS file
#     las = laspy.read(las_file_path)
#     points = las.xyz  # Extract x, y, z coordinates

#     # Step 2: Compute the bounding box in LAS file's coordinate system
#     min_x, min_y = points[:, 0].min(), points[:, 1].min()
#     max_x, max_y = points[:, 0].max(), points[:, 1].max()

#     # Step 3: Define projections using the updated API
#     # Create a transformer from UTM to WGS84
#     # The issue might be that the UTM coordinates are already in lat/lon format
#     # Let's print them and use them directly

#     print(f"UTM coordinates (min_x, min_y): ({min_x}, {min_y})")
#     print(f"UTM coordinates (max_x, max_y): ({max_x}, {max_y})")

#     # The coordinates from the LAS file appear to already be in WGS84 format
#     # (longitude around 13.55, latitude around 52.33)
#     min_lon, min_lat = min_x, min_y
#     max_lon, max_lat = max_x, max_y

#     print(f"Using coordinates directly from the LAS file")
#     print(f"WGS84 coordinates (min_lon, min_lat): ({min_lon}, {min_lat})")
#     print(f"WGS84 coordinates (max_lon, max_lat): ({max_lon}, {max_lat})")

#     # Step 5: Adjust for bounding box format (south, north, west, east)
#     south, north = min_lat, max_lat
#     west, east = min_lon, max_lon

#     # Step 6: Query OSM for road data within the bounding box
#     # Updated to use the current osmnx API
#     bbox = (north, south, east, west)
#     print(f"Querying OSM for road data within bounding box: {bbox}")

#     try:
#         # Use the coordinates from the LAS file
#         print(f"Querying OSM for road data using coordinates from the LAS file")

#         # Calculate the center point of the bounding box
#         center_lat = (north + south) / 2
#         center_lon = (east + west) / 2
#         center_point = (center_lat, center_lon)

#         # Calculate the approximate distance in meters (rough estimate)
#         # 1 degree of latitude is approximately 111 km
#         dist_meters = max(
#             abs(north - south) * 111000 / 2,  # half the height in meters
#             abs(east - west) * 111000 * cos(radians(center_lat)) / 2  # half the width in meters
#         )

#         # Limit the distance to a reasonable value to avoid timeouts
#         dist_meters = min(dist_meters, 1000)  # max 1 km

#         print(f"Using center point: {center_point} with distance: {dist_meters} meters")

#         G = ox.graph_from_point(center_point, dist=dist_meters, network_type='drive')

#         # Step 7: Convert OSM graph to GeoDataFrame (edges only)
#         gdf_edges = ox.graph_to_gdfs(G, edges=True, nodes=False)
#     except Exception as e:
#         print(f"Error querying OSM: {str(e)}")
#         print("Creating an empty GeoDataFrame with a point geometry instead")
#         # Create an empty GeoDataFrame with a point geometry
#         from shapely.geometry import Point
#         gdf_edges = gpd.GeoDataFrame(
#             {'id': [1], 'name': ['No roads found']},
#             geometry=[Point(min_lon, min_lat)],
#             crs="EPSG:4326"
#         )

#     # Step 8: Save the GeoDataFrame as GeoJSON
#     output_file = 'output.geojson'
#     gdf_edges.to_file(output_file, driver='GeoJSON')

#     return output_file

# # Example usage
# if __name__ == "__main__":
#     las_file = r'C:\Users\<USER>\Desktop\models\only_las_files\HT440_1739347507_3217836_1423385616059548_1423385678594855.las'  # Replace with actual LAS file path
#     utm_epsg = 32632  # Example: UTM 32N for Germany

#     # Check if the LAS file exists
#     if not os.path.exists(las_file):
#         print(f"Error: LAS file not found at {las_file}")
#         sys.exit(1)

#     try:
#         # Print information about the process
#         print(f"Processing LAS file: {las_file}")
#         print(f"Using UTM EPSG code: {utm_epsg}")

#         # Read the LAS file to get basic info
#         las = laspy.read(las_file)
#         points = las.xyz
#         min_x, min_y = points[:, 0].min(), points[:, 1].min()
#         max_x, max_y = points[:, 0].max(), points[:, 1].max()
#         print(f"LAS file contains {len(points)} points")
#         print(f"Bounding box in UTM coordinates: ({min_x}, {min_y}) to ({max_x}, {max_y})")

#         # Convert to WGS84 for display
#         transformer = Transformer.from_crs(f"EPSG:{utm_epsg}", "EPSG:4326", always_xy=True)
#         min_lon, min_lat = transformer.transform(min_x, min_y)
#         max_lon, max_lat = transformer.transform(max_x, max_y)
#         print(f"Bounding box in WGS84 coordinates: ({min_lon}, {min_lat}) to ({max_lon}, {max_lat})")

#         # Process the file
#         result = las_to_osm_geojson(las_file, utm_epsg)
#         print(f"GeoJSON saved to {result}")

#     except Exception as e:
#         print(f"Error processing file: {str(e)}")
#         import traceback
#         traceback.print_exc()
#         sys.exit(1)

#------------------------------------------------------------------------
# complex tests
#------------------------------------------------------------------------

"""
Extract lanes and lane-markings for the area covered by a LAS file
==================================================================

* Reads the LAS file to get a bounding box.
* Queries Overpass (via OSMnx) for:
    – every highway=* way that *already* has any “lanes*” key
    – every way/tag road_marking=*
* Turns each centre-line into per-lane geometry using Shapely.line.parallel_offset.
* Writes one GeoJSON:  layer=lanes   layer=road_markings
"""

from __future__ import annotations

import json
import os
import sys
from math import cos, radians
from pathlib import Path
from typing import List

import geopandas as gpd
import laspy
import osmnx as ox
import pandas as pd
from pyproj import CRS, Transformer
from shapely.geometry import LineString
from shapely.ops import unary_union

# -----------  parameters you are most likely to tweak  ------------ #

LANE_WIDTH_M = 3.5                 # default German Autobahn lane width
MAX_DOWNLOAD_METERS = 1_000        # Overpass timeout protection

# ----------------------------------------------------------------- #


def _explode_turn_lanes(row: pd.Series) -> List[dict]:
    """
    Turn composite *:lanes tags into one dict per lane.
    E.g. turn:lanes = 'left|through|through;right'
    """
    lanes_total = int(row.get("lanes", 1))
    turn_field = row.get("turn:lanes")
    dest_field = row.get("destination:lanes")
    out = []

    # Helper that pads the list to the total number of lanes
    def _parts(val):
        if not val or pd.isna(val):
            return [""] * lanes_total
        parts = val.split("|")
        if len(parts) < lanes_total:
            parts += [""] * (lanes_total - len(parts))
        return parts

    turn_parts = _parts(turn_field)
    dest_parts = _parts(dest_field)

    for idx in range(lanes_total):
        rec = row.to_dict()
        rec["lane_index"] = idx + 1
        rec["turn"] = turn_parts[idx]
        rec["destination"] = dest_parts[idx]
        out.append(rec)
    return out


def _offset_lanes(one_way_gdf: gpd.GeoDataFrame, crs_m: CRS) -> gpd.GeoDataFrame:
    """
    For each centre-line produce N parallel offsets – one per lane – to the right
    (positive offset in a projected CRS gives “left” of geometry direction, so
    we offset negative for driving on the right).
    """
    lanes_records = []
    one_way_m = one_way_gdf.to_crs(crs_m)

    for _, row in one_way_m.iterrows():
        lanes_total = int(row.get("lanes", 1))
        for ln in range(lanes_total):
            offset = -(LANE_WIDTH_M * (ln + 0.5))  # right side of line
            try:
                g = row.geometry.parallel_offset(offset, side="right", join_style=2)
            except ValueError:
                # very short segments sometimes fail to offset – fall back
                g = row.geometry
            r = row.to_dict()
            r["lane_index"] = ln + 1
            r["geometry"] = g
            lanes_records.append(r)

    lanes_gdf = gpd.GeoDataFrame(lanes_records, crs=crs_m).to_crs(one_way_gdf.crs)
    return lanes_gdf


def las_to_lane_geojson(las_path: str | Path, utm_epsg: int) -> Path:
    # ---------- 1  <USER> <GROUP> from LAS ----------------------------------- #
    las = laspy.read(str(las_path))
    pts = las.xyz
    min_x, min_y = pts[:, 0].min(), pts[:, 1].min()
    max_x, max_y = pts[:, 0].max(), pts[:, 1].max()

    # Print raw coordinates for debugging
    print(f"Raw coordinates from LAS file: X: {min_x} to {max_x}, Y: {min_y} to {max_y}")

    # Check if coordinates are already in WGS84 format (longitude/latitude)
    # Typical longitude range is -180 to 180, latitude range is -90 to 90
    if -180 <= min_x <= 180 and -180 <= max_x <= 180 and -90 <= min_y <= 90 and -90 <= max_y <= 90:
        print("Coordinates appear to be already in WGS84 format (longitude/latitude)")
        min_lon, min_lat = min_x, min_y
        max_lon, max_lat = max_x, max_y
    else:
        # ---------- 2  <USER> <GROUP> to WGS84 ------------------------------------ #
        print(f"Converting coordinates from UTM (EPSG:{utm_epsg}) to WGS84")
        tfm = Transformer.from_crs(f"EPSG:{utm_epsg}", "EPSG:4326", always_xy=True)
        min_lon, min_lat = tfm.transform(min_x, min_y)
        max_lon, max_lat = tfm.transform(max_x, max_y)

    print(f"Bounding box in WGS84: ({min_lon}, {min_lat}) to ({max_lon}, {max_lat})")
    south, north = min_lat, max_lat
    west, east = min_lon, max_lon

    # small buffer (~10 m) so lines touching the edge are included
    lat_buf = 10 / 111_000
    lon_buf = 10 / (111_000 * cos(radians((north + south) / 2)))
    south -= lat_buf
    north += lat_buf
    west -= lon_buf
    east += lon_buf

    # ---------- 3  <USER> <GROUP> data --------------------------------------- #
    tags = {
        "highway": True,                      # any road
        "lanes": True, "lanes:forward": True,
        "lanes:backward": True, "turn:lanes": True,
        "destination:lanes": True, "lane_markings": True,
        "road_marking": True,                 # separate marking ways
    }
    print("Querying Overpass…")
    # Create bbox tuple in the format (left, bottom, right, top)
    bbox = (west, south, east, north)
    print(f"Querying with bbox: {bbox}")
    print(f"Coordinates: west={west}, south={south}, east={east}, north={north}")

    # Try with a larger buffer to ensure we get some data
    buffer_km = 1.0  # 1 km buffer
    lat_buffer = buffer_km / 111.0  # approx 1 degree = 111 km
    lon_buffer = buffer_km / (111.0 * cos(radians((north + south) / 2)))

    bbox_buffered = (west - lon_buffer, south - lat_buffer, east + lon_buffer, north + lat_buffer)
    print(f"Using buffered bbox: {bbox_buffered}")

    try:
        gdf_all = ox.features.features_from_bbox(bbox_buffered, tags=tags)
    except Exception as e:
        print(f"Error querying OSM: {e}")
        print("Trying with a different approach...")

        # Try with a point and radius approach
        center_lat = (north + south) / 2
        center_lon = (west + east) / 2
        print(f"Using center point: ({center_lon}, {center_lat})")

        try:
            gdf_all = ox.features.features_from_point((center_lat, center_lon), tags=tags, dist=2000)
        except Exception as e:
            print(f"Error with point approach: {e}")
            raise RuntimeError("Could not retrieve OSM data for this area")
    if gdf_all.empty:
        raise RuntimeError("No ways with lane information in this bounding box")

    # ---------- 4  <USER> <GROUP> vs. road_markings ------------------------ #
    highways = gdf_all[gdf_all["highway"].notna()].copy()

    # Check if road_marking column exists
    if "road_marking" in gdf_all.columns:
        markings = gdf_all[gdf_all["road_marking"].notna()].copy()
    else:
        print("No road_marking column found in OSM data")
        markings = gpd.GeoDataFrame(geometry=[], crs=highways.crs)

    # keep only one-way motorway / motorway_link / trunk etc. for now
    highways = highways[
        highways["highway"].isin(
            ["motorway", "motorway_link", "trunk", "trunk_link", "primary", "primary_link"]
        )
    ]

    # ---------- 5  <USER> <GROUP> attributes ----------------------------- #
    # rows with composite *:lanes strings
    rows = []
    for _, r in highways.iterrows():
        rows.extend(_explode_turn_lanes(r))
    hw_exp = gpd.GeoDataFrame(rows, crs=highways.crs)

    # ---------- 6  <USER> <GROUP> geometries ----------------------------------- #
    # choose a metres-based CRS – we reuse the LAS file’s UTM
    crs_m = CRS.from_epsg(utm_epsg)
    lane_gdf = _offset_lanes(hw_exp, crs_m)

    # ---------- 7  <USER> <GROUP> & export ----------------------------------- #
    # Define columns we want to keep
    lane_cols_keep = [
        "ref",
        "highway",
        "maxspeed",
        "oneway",
        "lane_index",
        "turn",
        "destination",
        "geometry",
    ]

    # Check which columns actually exist in the dataframe
    available_cols = [col for col in lane_cols_keep if col in lane_gdf.columns]

    # Make sure geometry is included but only once
    if "geometry" not in available_cols:
        available_cols.append("geometry")

    # Add osmid if it exists
    if "osmid" in lane_gdf.columns and "osmid" not in available_cols:
        available_cols.append("osmid")

    # Print available columns for debugging
    print(f"Available columns: {lane_gdf.columns.tolist()}")
    print(f"Keeping columns: {available_cols}")

    # Filter to only keep available columns
    lane_gdf = lane_gdf[available_cols]

    # GeoJSON cannot hold multiple layers natively, but many GIS programmes
    # understand an array. We therefore write one FeatureCollection with two
    # top-level keys.
    tmp_dir = Path(las_path).with_suffix("")  # same folder as LAS
    tmp_dir.mkdir(exist_ok=True)
    out_geojson = tmp_dir / f"{Path(las_path).stem}_lanes.geojson"

    # write as two GeoJSON files then merge
    lanes_tmp = tmp_dir / "lanes_tmp.geojson"
    marks_tmp = tmp_dir / "markings_tmp.geojson"
    lane_gdf.to_file(lanes_tmp, driver="GeoJSON")
    if not markings.empty:
        markings.to_file(marks_tmp, driver="GeoJSON")

    with open(lanes_tmp) as f:
        lanes_fc = json.load(f)
    if marks_tmp.exists():
        with open(marks_tmp) as f:
            marks_fc = json.load(f)
    else:
        marks_fc = {"type": "FeatureCollection", "features": []}

    out_fc = {
        "type": "FeatureCollection",
        "name": "lanes_and_markings",
        "features": (
            [{"properties": {"layer": "lane", **f["properties"]}, "geometry": f["geometry"], "type": "Feature"}
             for f in lanes_fc["features"]]
            +
            [{"properties": {"layer": "road_marking", **f["properties"]},
              "geometry": f["geometry"], "type": "Feature"}
             for f in marks_fc["features"]]
        ),
    }
    with open(out_geojson, "w", encoding="utf-8") as f:
        json.dump(out_fc, f, ensure_ascii=False)

    # clean temp
    lanes_tmp.unlink(missing_ok=True)
    marks_tmp.unlink(missing_ok=True)

    print(f"Successfully wrote {len(lane_gdf)} lane features and {len(markings)} marking features to {out_geojson}")
    return out_geojson


# ------------------------------------------------------------------------- #
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Extract lane data from LAS file using OpenStreetMap')
    parser.add_argument('--las', type=str, required=True, help='Path to LAS file')
    parser.add_argument('--utm_epsg', type=int, default=32632, help='UTM EPSG code for coordinate transformation')
    parser.add_argument('--output_dir', type=str, help='Optional output directory (default: same as LAS file)')

    args = parser.parse_args()

    LAS_FILE = args.las
    UTM_EPSG = args.utm_epsg

    if not Path(LAS_FILE).exists():
        sys.exit(f"LAS file not found: {LAS_FILE}")

    try:
        geojson_path = las_to_lane_geojson(LAS_FILE, UTM_EPSG)
        print(f"GeoJSON with lanes + markings saved to: {geojson_path}")
    except Exception as exc:
        print(f"🛑 Error: {exc}", file=sys.stderr)
        raise
