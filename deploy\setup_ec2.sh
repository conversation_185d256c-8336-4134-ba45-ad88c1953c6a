#!/bin/bash
# Setup script for LAS processor on EC2 instance

set -e  # Exit on error

echo "Setting up LAS processor environment..."

# Update system packages
echo "Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# Install system dependencies
echo "Installing system dependencies..."
sudo apt-get install -y \
    python3-pip \
    python3-dev \
    python3-venv \
    gdal-bin \
    libgdal-dev \
    libspatialindex-dev \
    zip \
    unzip \
    git

# Create a virtual environment
echo "Creating Python virtual environment..."
python3 -m venv ~/las_processor_env
source ~/las_processor_env/bin/activate

# Install GDAL with correct version
GDAL_VERSION=$(gdal-config --version)
echo "Installing GDAL Python bindings (version $GDAL_VERSION)..."
pip install GDAL==$GDAL_VERSION

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Create directories for processing
echo "Creating processing directories..."
mkdir -p ~/las_processor/input
mkdir -p ~/las_processor/output
mkdir -p ~/las_processor/logs

# Copy the processing script
echo "Setting up processing script..."
cp precise_las_to_lane_geojson.py ~/las_processor/
cp lambda_handler.py ~/las_processor/

# Create a wrapper script for processing files from S3
cat > ~/las_processor/process_from_s3.py << 'EOF'
#!/usr/bin/env python3
"""
Process LAS files from S3 bucket and save results back to S3
"""
import os
import sys
import boto3
import logging
import argparse
import tempfile
from pathlib import Path
import precise_las_to_lane_geojson as processor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("las_processor.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def process_s3_file(bucket, key, utm_epsg=32632, sample_rate=0.01, alpha=100.0):
    """
    Process a LAS file from S3 and save the result back to S3
    
    Args:
        bucket: S3 bucket name
        key: S3 object key (path to LAS file)
        utm_epsg: UTM EPSG code for coordinate transformation
        sample_rate: Fraction of points to sample
        alpha: Alpha value for the alpha shape algorithm
        
    Returns:
        Dict with processing results
    """
    s3 = boto3.client('s3')
    
    logger.info(f"Processing file s3://{bucket}/{key}")
    
    # Check if the file is a LAS file
    if not key.lower().endswith('.las'):
        logger.info(f"Skipping non-LAS file: {key}")
        return {"status": "skipped", "reason": "Not a LAS file"}
    
    # Create temporary directory for processing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Download the LAS file
        las_path = os.path.join(temp_dir, os.path.basename(key))
        s3.download_file(bucket, key, las_path)
        
        logger.info(f"Downloaded {key} to {las_path}")
        
        # Determine output path
        output_filename = f"{Path(las_path).stem}_lanes.geojson"
        output_path = os.path.join(temp_dir, output_filename)
        
        try:
            # Process the LAS file
            result_path = processor.process_las_file(
                las_path=las_path,
                utm_epsg=utm_epsg,
                output_path=output_path,
                sample_rate=sample_rate,
                alpha=alpha
            )
            
            logger.info(f"Processing complete. Result saved to {result_path}")
            
            # Upload the result to S3
            output_key = f"results/{output_filename}"
            s3.upload_file(result_path, bucket, output_key)
            
            logger.info(f"Uploaded result to s3://{bucket}/{output_key}")
            
            # Generate a pre-signed URL for the result
            url = s3.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': output_key},
                ExpiresIn=604800  # URL valid for 7 days
            )
            
            return {
                "status": "success",
                "input_file": key,
                "output_file": output_key,
                "download_url": url
            }
            
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "error": str(e)
            }

def main():
    parser = argparse.ArgumentParser(description='Process LAS files from S3 bucket')
    parser.add_argument('--bucket', type=str, required=True, help='S3 bucket name')
    parser.add_argument('--key', type=str, required=True, help='S3 object key (path to LAS file)')
    parser.add_argument('--utm-epsg', type=int, default=32632, help='UTM EPSG code for coordinate transformation')
    parser.add_argument('--sample-rate', type=float, default=0.01, help='Fraction of points to sample (0.01 = 1%)')
    parser.add_argument('--alpha', type=float, default=100.0, help='Alpha value for the alpha shape algorithm')
    
    args = parser.parse_args()
    
    result = process_s3_file(
        args.bucket,
        args.key,
        args.utm_epsg,
        args.sample_rate,
        args.alpha
    )
    
    print(result)

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x ~/las_processor/process_from_s3.py

# Create a systemd service for continuous processing
cat > /tmp/las-processor.service << 'EOF'
[Unit]
Description=LAS Processor Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/las_processor
ExecStart=/home/<USER>/las_processor_env/bin/python /home/<USER>/las_processor/process_from_s3.py
Restart=on-failure
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF

sudo mv /tmp/las-processor.service /etc/systemd/system/
sudo systemctl daemon-reload

# Create a CloudWatch configuration file
mkdir -p /tmp/amazon-cloudwatch-agent
cat > /tmp/amazon-cloudwatch-agent/amazon-cloudwatch-agent.json << 'EOF'
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "root"
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/home/<USER>/las_processor/las_processor.log",
            "log_group_name": "las-processor-logs",
            "log_stream_name": "{instance_id}-las-processor",
            "retention_in_days": 14
          }
        ]
      }
    }
  },
  "metrics": {
    "metrics_collected": {
      "disk": {
        "measurement": [
          "used_percent"
        ],
        "resources": [
          "*"
        ]
      },
      "mem": {
        "measurement": [
          "mem_used_percent"
        ]
      }
    },
    "append_dimensions": {
      "InstanceId": "${aws:InstanceId}"
    }
  }
}
EOF

# Install CloudWatch agent
echo "Installing CloudWatch agent..."
wget https://s3.amazonaws.com/amazoncloudwatch-agent/ubuntu/amd64/latest/amazon-cloudwatch-agent.deb
sudo dpkg -i amazon-cloudwatch-agent.deb
sudo mv /tmp/amazon-cloudwatch-agent/amazon-cloudwatch-agent.json /opt/aws/amazon-cloudwatch-agent/etc/
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json

echo "Setup complete!"
echo "You can now process LAS files using:"
echo "source ~/las_processor_env/bin/activate"
echo "python ~/las_processor/process_from_s3.py --bucket YOUR_BUCKET --key path/to/file.las"
